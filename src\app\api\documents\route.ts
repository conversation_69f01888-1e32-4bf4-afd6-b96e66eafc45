import { NextRequest, NextResponse } from 'next/server';
import { DocumentModel, CreateDocumentData } from '@/lib/models/document';
import { getUserFromSession } from '@/lib/auth-utils';

// GET /api/documents - Get all documents
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit');
    const status = searchParams.get('status') as "pending" | "approved" | "rejected" | null;

    // Get user from session for filtering
    const user = await getUserFromSession(request);

    let documents;
    if (status) {
      documents = await DocumentModel.getByStatus(status, user?.id);
    } else {
      documents = await DocumentModel.findAll(
        limit ? parseInt(limit) : undefined,
        user?.id
      );
    }
    
    // Transform documents to include pdfUrl and parse pdfData if it exists
    const transformedDocuments = documents.map(document => {
      let parsedPdfData = null;
      if (document.pdfData) {
        try {
          parsedPdfData = typeof document.pdfData === 'string'
            ? JSON.parse(document.pdfData)
            : document.pdfData;
        } catch (parseError) {
          console.error('Error parsing pdfData for document:', document.id, parseError);
          parsedPdfData = null;
        }
      }

      return {
        ...document,
        pdfData: parsedPdfData,
        pdfUrl: document.pdfData ? `/api/documents/${document.id}/pdf` : undefined
      };
    });

    return NextResponse.json({
      success: true,
      documents: transformedDocuments
    });
  } catch (error) {
    console.error('Error fetching documents:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch documents' },
      { status: 500 }
    );
  }
}

// POST /api/documents - Create a new document
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, message, type, pdfFileName, pdfData, status } = body;

    if (!title || !message || !type) {
      return NextResponse.json(
        { success: false, error: 'Title, message, and type are required' },
        { status: 400 }
      );
    }

    // Get user from session
    const user = await getUserFromSession(request);

    const documentData: CreateDocumentData = {
      title,
      message,
      type,
      pdfFileName,
      pdfData,
      userId: user?.id,
      status: status || 'pending'
    };

    const document = await DocumentModel.create(documentData);
    
    return NextResponse.json({
      success: true,
      document
    });
  } catch (error) {
    console.error('Error creating document:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create document' },
      { status: 500 }
    );
  }
}

// DELETE /api/documents - Delete all documents
export async function DELETE() {
  try {
    const deletedCount = await DocumentModel.deleteAll();
    
    return NextResponse.json({
      success: true,
      deletedCount
    });
  } catch (error) {
    console.error('Error deleting documents:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete documents' },
      { status: 500 }
    );
  }
}
