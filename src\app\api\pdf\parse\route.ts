import { NextRequest, NextResponse } from 'next/server';
import { unlink } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { NotificationModel } from '@/lib/models/notification';
import { DocumentModel } from '@/lib/models/document';
import { loadTemplates } from '@/lib/templates';

// For server-side PDF processing, we'll return the PDF data and let the client handle image conversion

// Validate if the PDF was generated by our system and check template compatibility
async function validatePDFCompatibility(parsedData: any): Promise<{ isValid: boolean; error?: string; templateExists?: boolean }> {
  try {
    // Check if the PDF has the required LDIS structure
    if (!parsedData.templateId || !parsedData.templateName || !parsedData.userData || !parsedData.generatedAt) {
      return {
        isValid: false,
        error: 'PDF was not generated by LDIS system. Missing required embedded data structure.'
      };
    }

    // Load all available templates to validate template exists
    const templates = await loadTemplates();
    const templateExists = templates.some(template => template.id === parsedData.templateId);

    if (!templateExists) {
      return {
        isValid: false,
        error: `Template '${parsedData.templateName}' (ID: ${parsedData.templateId}) not found in system. The PDF may be from an older version or different system.`,
        templateExists: false
      };
    }

    // Additional validation: check if the embedded data has the expected structure
    if (!parsedData.placeholders || !Array.isArray(parsedData.placeholders)) {
      return {
        isValid: false,
        error: 'Invalid PDF structure: missing or invalid placeholders data.'
      };
    }

    return { isValid: true, templateExists: true };
  } catch (error) {
    console.error('Error validating PDF compatibility:', error);
    return {
      isValid: false,
      error: 'Failed to validate PDF compatibility. Please try again.'
    };
  }
}

// Optimized function to search for embedded data without converting entire buffer to string
function findEmbeddedData(buffer: Buffer): { hasEmbeddedData: boolean; data: any } {
  const startMarker = Buffer.from('LDIS_DATA_BEGIN:', 'utf8');
  const endMarker = Buffer.from(':LDIS_DATA_END', 'utf8');

  const startIndex = buffer.indexOf(startMarker);
  if (startIndex === -1) {
    return { hasEmbeddedData: false, data: null };
  }

  const endIndex = buffer.indexOf(endMarker, startIndex);
  if (endIndex === -1) {
    return { hasEmbeddedData: false, data: null };
  }

  try {
    // Extract only the JSON portion
    const jsonStart = startIndex + startMarker.length;
    const jsonBuffer = buffer.subarray(jsonStart, endIndex);
    const jsonData = jsonBuffer.toString('utf8');
    const parsedData = JSON.parse(jsonData);

    return { hasEmbeddedData: true, data: parsedData };
  } catch (parseError) {
    console.error('Error parsing embedded JSON:', parseError);
    return { hasEmbeddedData: false, data: null };
  }
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const oldFilePath = formData.get('oldFilePath') as string;

    if (!file) {
      return NextResponse.json(
        { error: 'PDF file is required' },
        { status: 400 }
      );
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Extract embedded data from PDF
    const { hasEmbeddedData, data: parsedData } = findEmbeddedData(buffer);

    if (!hasEmbeddedData || !parsedData) {
      return NextResponse.json(
        { error: 'No valid embedded data found in PDF. This file was not generated by LDIS system.' },
        { status: 400 }
      );
    }

    // Validate PDF compatibility and template existence
    const validation = await validatePDFCompatibility(parsedData);
    if (!validation.isValid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }

    // Create metadata with enhanced information
    const metadata = {
      ...parsedData,
      status: parsedData.status || null, // Default to null if no status
      originalFileName: file.name,
      uploadedAt: new Date().toISOString(),
      fileSize: file.size,
      extractedAt: new Date().toISOString()
    };

    try {
      // Extract applicant name from userData (new structure)
      const userData = metadata.userData || {};

      // Extract name fields by searching through all userData keys
      const userDataKeys = Object.keys(userData);

      // Find first name field
      const firstNameKey = userDataKeys.find(key => {
        const lowerKey = key.toLowerCase();
        return lowerKey.includes('first') && lowerKey.includes('name');
      });

      // Find last name field
      const lastNameKey = userDataKeys.find(key => {
        const lowerKey = key.toLowerCase();
        return (lowerKey.includes('last') && lowerKey.includes('name')) ||
               lowerKey.includes('surname') ||
               lowerKey === 'lastname';
      });

      // Find middle name/initial field
      const middleNameKey = userDataKeys.find(key => {
        const lowerKey = key.toLowerCase();
        return (lowerKey.includes('middle') && (lowerKey.includes('name') || lowerKey.includes('initial'))) ||
               lowerKey === 'middleinitial' ||
               lowerKey === 'middle_initial';
      });

      const firstName = firstNameKey ? userData[firstNameKey] : '';
      const lastName = lastNameKey ? userData[lastNameKey] : '';
      const middleInitial = middleNameKey ? userData[middleNameKey] : '';

      let applicantName = [firstName, middleInitial, lastName]
        .filter(name => name && name.trim())
        .join(' ')
        .trim();

      // If no name found in individual fields, try common name field variations
      if (!applicantName) {
        const nameFields = ['name', 'fullName', 'applicantName', 'clientName'];
        for (const field of nameFields) {
          if (userData[field] && userData[field].trim()) {
            applicantName = userData[field].trim();
            break;
          }
        }
      }

      // If still no name, try to extract from filename
      if (!applicantName) {
        let nameFromFile = file.name.replace('.pdf', '');

        // Check if filename has format "Template Name (Applicant Name)"
        const parenthesesMatch = nameFromFile.match(/^.*?\(([^)]+)\)$/);
        if (parenthesesMatch) {
          // Extract name from parentheses
          applicantName = parenthesesMatch[1].trim();
        } else {
          // Use the whole filename, cleaned up
          applicantName = nameFromFile
            .replace(/[_-]/g, ' ') // Replace underscores and dashes with spaces
            .replace(/\s+/g, ' ') // Replace multiple spaces with single space
            .trim();
        }
      }

      // First, create a document in the documents table
      const document = await DocumentModel.create({
        title: `${metadata.templateName || 'Unknown Template'}`,
        message: `Document uploaded by ${applicantName}`,
        type: 'info',
        pdfFileName: file.name, // Store original PDF filename
        pdfData: metadata, // Store all metadata as PDFData object
        status: 'pending' // New documents start as pending
      });

      // Then create a notification that references the document
      const notification = await NotificationModel.create({
        title: `New Document: ${metadata.templateName || 'Unknown Template'}`,
        message: `Document uploaded by ${applicantName}`,
        type: 'info',
        documentId: document.id // Reference the created document
      });

      console.log('Document created with ID:', document.id);
      console.log('Notification created with ID:', notification.id);
      console.log('Template validated:', metadata.templateName);

      // Return both document and notification information
      return NextResponse.json({
        success: true,
        hasEmbeddedData: true,
        data: metadata,
        document: {
          id: document.id,
          title: document.title,
          message: document.message,
          status: document.status
        },
        notification: {
          id: notification.id,
          title: notification.title,
          message: notification.message,
          documentId: document.id
        },
        templateId: metadata.templateId,
        templateName: metadata.templateName,
        originalFileName: file.name,
        fileSize: file.size,
        applicantName: applicantName,
        message: 'PDF processed successfully, document and notification created'
      });

    } catch (saveError) {
      console.error('Error saving to database:', saveError);
      return NextResponse.json(
        { error: 'Failed to save data to database' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error parsing PDF:', error);
    return NextResponse.json(
      { error: 'Failed to parse PDF file' },
      { status: 500 }
    );
  }
}
