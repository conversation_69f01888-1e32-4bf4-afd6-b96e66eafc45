import { NextRequest, NextResponse } from 'next/server';
import { getArchivedDocument } from '@/lib/database';
import { createDocumentQRCode, getDocumentQRCode, updateQRCodeScanCount } from '@/lib/database';
import { getClientBaseURL } from '@/lib/network-utils';
import QRCode from 'qrcode';

// GET /api/documents/[id]/qr-code - Get or generate QR code for approved document
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Check if document exists in archives (only approved documents have QR codes)
    const archivedDoc = await getArchivedDocument(id);
    if (!archivedDoc) {
      return NextResponse.json(
        { success: false, error: 'Document not found or not approved' },
        { status: 404 }
      );
    }

    // Check if QR code already exists
    let qrCode = await getDocumentQRCode(id);

    if (!qrCode) {
      // Generate new QR code
      const baseURL = await getClientBaseURL();
      const validationUrl = `${baseURL}/validate/${id}`;

      // Generate QR code data URL
      const qrCodeDataUrl = await QRCode.toDataURL(validationUrl, {
        width: 200,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      });

      // Save QR code to database
      qrCode = await createDocumentQRCode({
        document_id: id,
        qr_code_data: qrCodeDataUrl,
        validation_url: validationUrl,
      });
    }

    return NextResponse.json({
      success: true,
      qrCode: {
        id: qrCode.id,
        document_id: qrCode.document_id,
        qr_code_data: qrCode.qr_code_data,
        validation_url: qrCode.validation_url,
        generated_at: qrCode.generated_at,
        scan_count: qrCode.scan_count,
        last_scanned_at: qrCode.last_scanned_at,
      },
      document: {
        id: archivedDoc.id,
        template_name: archivedDoc.template_name,
        applicant_name: archivedDoc.applicant_name,
        approved_at: archivedDoc.approved_at,
        approved_by: archivedDoc.approved_by,
      }
    });

  } catch (error) {
    console.error('Error generating QR code:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate QR code' },
      { status: 500 }
    );
  }
}

// POST /api/documents/[id]/qr-code - Record QR code scan
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Update scan count
    const updated = await updateQRCodeScanCount(id);

    if (!updated) {
      return NextResponse.json(
        { success: false, error: 'QR code not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Scan count updated'
    });

  } catch (error) {
    console.error('Error updating scan count:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update scan count' },
      { status: 500 }
    );
  }
}
