"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { QrCode, Download, Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";

interface QRCodeData {
  id: string;
  document_id: string;
  qr_code_data: string;
  validation_url: string;
  generated_at: string;
  scan_count: number;
  last_scanned_at?: string;
}

interface QRCodeSectionProps {
  documentId: string;
}

export function QRCodeSection({ documentId }: QRCodeSectionProps) {
  const [qrCode, setQrCode] = useState<QRCodeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [showQR, setShowQR] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchQRCode = async () => {
      try {
        const response = await fetch(`/api/documents/${documentId}/qr-code`);
        const data = await response.json();

        if (data.success) {
          setQrCode(data.qrCode);
        } else {
          setError(data.error || "Failed to load QR code");
        }
      } catch (err) {
        console.error("Error fetching QR code:", err);
        setError("Failed to load QR code");
      } finally {
        setLoading(false);
      }
    };

    fetchQRCode();
  }, [documentId]);

  const handleDownloadQR = () => {
    if (!qrCode) return;

    // Create a download link for the QR code
    const link = document.createElement("a");
    link.href = qrCode.qr_code_data;
    link.download = `qr-code-${documentId}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success("QR code downloaded successfully");
  };

  const copyValidationUrl = async () => {
    if (!qrCode) return;

    try {
      await navigator.clipboard.writeText(qrCode.validation_url);
      toast.success("Validation URL copied to clipboard");
    } catch (err) {
      console.error("Failed to copy URL:", err);
      toast.error("Failed to copy URL");
    }
  };

  if (loading) {
    return (
      <div className="text-sm text-muted-foreground">Loading QR code...</div>
    );
  }

  if (error) {
    return <div className="text-sm text-red-600">{error}</div>;
  }

  if (!qrCode) {
    return (
      <div className="text-sm text-muted-foreground">No QR code available</div>
    );
  }

  return (
    <div className="space-y-3">
      {/* QR Code Stats */}
      <div className="flex items-center gap-2">
        <Badge variant="secondary" className="text-xs">
          <QrCode className="h-3 w-3 mr-1" />
          Scanned {qrCode.scan_count} times
        </Badge>
      </div>

      {/* QR Code Display Toggle */}
      <div className="space-y-2">
        <Button
          onClick={() => setShowQR(!showQR)}
          variant="outline"
          size="sm"
          className="w-full"
        >
          {showQR ? (
            <>
              <EyeOff className="h-4 w-4 mr-2" />
              Hide QR Code
            </>
          ) : (
            <>
              <Eye className="h-4 w-4 mr-2" />
              Show QR Code
            </>
          )}
        </Button>

        {showQR && (
          <div className="space-y-2">
            {/* QR Code Image */}
            <div className="flex justify-center p-4 bg-white rounded-lg border">
              <img
                src={qrCode.qr_code_data}
                alt="Document QR Code"
                className="w-32 h-32"
              />
            </div>

            {/* QR Code Actions */}
            <div className="flex gap-2">
              <Button
                onClick={handleDownloadQR}
                variant="outline"
                size="sm"
                className="flex-1"
              >
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
              <Button
                onClick={copyValidationUrl}
                variant="outline"
                size="sm"
                className="flex-1"
              >
                Copy URL
              </Button>
            </div>

            {/* Validation URL */}
            <div className="text-xs text-muted-foreground break-all">
              <strong>Validation URL:</strong>
              <br />
              {qrCode.validation_url}
            </div>
          </div>
        )}
      </div>

      {/* Last Scanned */}
      {qrCode.last_scanned_at && (
        <div className="text-xs text-muted-foreground">
          Last scanned: {new Date(qrCode.last_scanned_at).toLocaleString()}
        </div>
      )}
    </div>
  );
}
