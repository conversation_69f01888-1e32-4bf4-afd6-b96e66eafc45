import { NextRequest, NextResponse } from 'next/server';
import { DocumentModel } from '@/lib/models/document';
import { getUserFromSession } from '@/lib/auth-utils';

// GET /api/documents/[id] - Get a specific document
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Get user from session for filtering
    const user = await getUserFromSession(request);

    const document = await DocumentModel.findById(id, user?.id);

    if (!document) {
      return NextResponse.json(
        { success: false, error: 'Document not found' },
        { status: 404 }
      );
    }

    // Parse pdfData if it exists and is a string
    let parsedPdfData = null;
    if (document.pdfData) {
      try {
        parsedPdfData = typeof document.pdfData === 'string'
          ? JSON.parse(document.pdfData)
          : document.pdfData;
      } catch (parseError) {
        console.error('Error parsing pdfData:', parseError);
        parsedPdfData = null;
      }
    }

    // Transform the document to include pdfUrl and parsed pdfData
    const transformedDocument = {
      ...document,
      pdfData: parsedPdfData,
      pdfUrl: document.pdfData ? `/api/documents/${id}/pdf` : undefined
    };

    return NextResponse.json({
      success: true,
      document: transformedDocument
    });
  } catch (error) {
    console.error('Error fetching document:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch document' },
      { status: 500 }
    );
  }
}

// PATCH /api/documents/[id] - Update a document (e.g., mark as read, update content, approve/reject)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { isRead, title, message, pdfFileName, pdfData, status, approvedBy } = body;

    // Handle marking as read
    if (isRead === true) {
      const success = await DocumentModel.markAsRead(id);

      if (!success) {
        return NextResponse.json(
          { success: false, error: 'Document not found' },
          { status: 404 }
        );
      }

      const updatedDocument = await DocumentModel.findById(id);

      return NextResponse.json({
        success: true,
        document: updatedDocument
      });
    }

    // Handle document update (for edit functionality or approval/rejection)
    if (title || message || pdfFileName || pdfData || status) {
      const updateData: any = {};

      if (title) updateData.title = title;
      if (message) updateData.message = message;
      if (pdfFileName) updateData.pdfFileName = pdfFileName;
      if (pdfData) updateData.pdfData = pdfData;
      if (status) {
        updateData.status = status;
        if (status === 'approved') {
          updateData.approvedAt = new Date().toISOString();
          updateData.approvedBy = approvedBy || 'admin';
        }
      }

      const success = await DocumentModel.update(id, updateData);

      if (!success) {
        return NextResponse.json(
          { success: false, error: 'Document not found' },
          { status: 404 }
        );
      }

      const updatedDocument = await DocumentModel.findById(id);

      // Parse pdfData if it exists and is a string
      let parsedPdfData = null;
      if (updatedDocument?.pdfData) {
        try {
          parsedPdfData = typeof updatedDocument.pdfData === 'string'
            ? JSON.parse(updatedDocument.pdfData)
            : updatedDocument.pdfData;
        } catch (parseError) {
          console.error('Error parsing pdfData:', parseError);
          parsedPdfData = null;
        }
      }

      // Transform the document to include pdfUrl and parsed pdfData
      const transformedDocument = {
        ...updatedDocument,
        pdfData: parsedPdfData,
        pdfUrl: updatedDocument?.pdfData ? `/api/documents/${id}/pdf` : undefined
      };

      return NextResponse.json({
        success: true,
        document: transformedDocument
      });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid update operation' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error updating document:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update document' },
      { status: 500 }
    );
  }
}

// DELETE /api/documents/[id] - Delete a specific document
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const success = await DocumentModel.delete(id);
    
    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Document not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: 'Document deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting document:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete document' },
      { status: 500 }
    );
  }
}
