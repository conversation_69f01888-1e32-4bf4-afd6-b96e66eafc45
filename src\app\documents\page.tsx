"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  Search,
  User,
  Calendar,
  FileType,
  Trash2,
  X,
  <PERSON>fresh<PERSON><PERSON>,
  Archive,
} from "lucide-react";
import { toast } from "sonner";
import { PDFData } from "@/lib/models/notification";

interface DocumentData {
  id: string;
  title: string;
  message: string;
  type: string;
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
  pdfFileName?: string;
  pdfData?: PDFData;
  userId?: number;
  status: "pending" | "approved" | "rejected";
  approvedAt?: string;
  approvedBy?: string;
  pdfUrl?: string;
}

export default function DocumentsPage() {
  const [documents, setDocuments] = useState<DocumentData[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<DocumentData[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedDocuments, setSelectedDocuments] = useState<Set<string>>(
    new Set()
  );
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<DocumentData | null>(
    null
  );
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [isArchiveDialogOpen, setIsArchiveDialogOpen] = useState(false);
  const [documentToArchive, setDocumentToArchive] =
    useState<DocumentData | null>(null);

  const router = useRouter();

  // Fetch documents on mount and set up polling
  useEffect(() => {
    fetchDocuments();

    // Set up automatic polling every 30 seconds
    const interval = setInterval(() => {
      fetchDocuments(true); // Silent refresh
    }, 30000);

    // Refresh when window gains focus
    const handleFocus = () => {
      fetchDocuments(true); // Silent refresh
    };

    window.addEventListener("focus", handleFocus);

    return () => {
      clearInterval(interval);
      window.removeEventListener("focus", handleFocus);
    };
  }, []);

  // Filter documents based on search and status
  useEffect(() => {
    let filtered = documents;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter((doc) => {
        const lowerSearchTerm = searchTerm.toLowerCase();

        // Check title and message
        if (
          doc.title.toLowerCase().includes(lowerSearchTerm) ||
          doc.message.toLowerCase().includes(lowerSearchTerm)
        ) {
          return true;
        }

        // Check applicant name using dynamic field detection
        if (doc.pdfData?.userData) {
          const userData = doc.pdfData.userData;
          const userDataKeys = Object.keys(userData);

          // Find and check all name fields
          const nameKeys = userDataKeys.filter((key) => {
            const lowerKey = key.toLowerCase();
            return lowerKey.includes("name") || lowerKey.includes("initial");
          });

          // Check if any name field contains the search term
          return nameKeys.some((key) => {
            const value = userData[key];
            return value && value.toLowerCase().includes(lowerSearchTerm);
          });
        }

        return false;
      });
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((doc) => {
        const status = doc.status || "pending";
        return status === statusFilter;
      });
    }

    setFilteredDocuments(filtered);
  }, [documents, searchTerm, statusFilter]);

  const fetchDocuments = async (silent = false) => {
    if (!silent) {
      setLoading(true);
    } else {
      setRefreshing(true);
    }

    try {
      const response = await fetch("/api/documents");
      if (response.ok) {
        const data = await response.json();

        // Debug logging
        console.log(`Fetched ${data.documents?.length || 0} total documents`);

        // All documents from the documents API should have PDF data
        const documents = data.documents || [];

        console.log(`Documents loaded: ${documents.length}`);

        setDocuments(documents);

        if (!silent) {
          console.log(
            `Document Management: Loaded ${documents.length} documents`
          );
        }
      } else {
        console.error(
          `Failed to fetch documents: ${response.status} ${response.statusText}`
        );
        if (!silent) {
          toast.error("Failed to fetch documents");
        }
      }
    } catch (error) {
      console.error("Error fetching documents:", error);
      if (!silent) {
        toast.error("Error fetching documents");
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Manual refresh function
  const handleRefresh = () => {
    fetchDocuments();
    toast.success("Documents refreshed");
  };

  const handleCardClick = (document: DocumentData) => {
    router.push(`/documents/${document.id}`);
  };

  // Handle checkbox selection
  const handleDocumentSelect = (documentId: string, checked: boolean) => {
    const newSelected = new Set(selectedDocuments);
    if (checked) {
      newSelected.add(documentId);
    } else {
      newSelected.delete(documentId);
    }
    setSelectedDocuments(newSelected);
  };

  // Handle select all checkbox
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allIds = new Set(filteredDocuments.map((doc) => doc.id));
      setSelectedDocuments(allIds);
    } else {
      setSelectedDocuments(new Set());
    }
  };

  // Handle single document delete
  const handleDeleteDocument = (document: DocumentData) => {
    setDocumentToDelete(document);
    setIsDeleteDialogOpen(true);
  };

  // Handle single document archive
  const handleArchiveDocument = (document: DocumentData) => {
    setDocumentToArchive(document);
    setIsArchiveDialogOpen(true);
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedDocuments.size === 0) {
      toast.error("Please select documents to delete");
      return;
    }
    setIsBulkDeleteDialogOpen(true);
  };

  // Confirm single document delete
  const confirmDeleteDocument = async () => {
    if (!documentToDelete) return;

    try {
      const response = await fetch(`/api/documents/${documentToDelete.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Document deleted successfully");
        setIsDeleteDialogOpen(false);
        setDocumentToDelete(null);
        fetchDocuments(); // Refresh the list
      } else {
        toast.error("Failed to delete document");
      }
    } catch (error) {
      console.error("Error deleting document:", error);
      toast.error("Error deleting document");
    }
  };

  // Confirm bulk delete
  const confirmBulkDelete = async () => {
    try {
      const deletePromises = Array.from(selectedDocuments).map((id) =>
        fetch(`/api/documents/${id}`, { method: "DELETE" })
      );

      const results = await Promise.all(deletePromises);
      const successCount = results.filter((r) => r.ok).length;
      const failCount = results.length - successCount;

      if (successCount > 0) {
        toast.success(`${successCount} document(s) deleted successfully`);
      }
      if (failCount > 0) {
        toast.error(`Failed to delete ${failCount} document(s)`);
      }

      setIsBulkDeleteDialogOpen(false);
      setSelectedDocuments(new Set());
      fetchDocuments(); // Refresh the list
    } catch (error) {
      console.error("Error deleting documents:", error);
      toast.error("Error deleting documents");
    }
  };

  // Confirm single document archive
  const confirmArchiveDocument = async () => {
    if (!documentToArchive) return;

    try {
      const response = await fetch(
        `/api/documents/${documentToArchive.id}/approve`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            approvedBy: "admin",
          }),
        }
      );

      if (response.ok) {
        toast.success("Document approved and archived successfully");
        setIsArchiveDialogOpen(false);
        setDocumentToArchive(null);
        fetchDocuments(); // Refresh the list
      } else {
        const errorData = await response.json();
        toast.error(
          errorData.error || "Failed to approve and archive document"
        );
      }
    } catch (error) {
      console.error("Error approving and archiving document:", error);
      toast.error("Error approving and archiving document");
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return (
          <Badge variant="default" className="bg-green-500">
            <CheckCircle className="w-3 h-3 mr-1" />
            Approved
          </Badge>
        );
      case "rejected":
        return (
          <Badge variant="destructive">
            <XCircle className="w-3 h-3 mr-1" />
            Rejected
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary">
            <Clock className="w-3 h-3 mr-1" />
            Pending
          </Badge>
        );
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading documents...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">Document Management</h1>
            <p className="text-muted-foreground">
              Manage uploaded documents, review submissions, and handle
              approvals.
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading || refreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`h-4 w-4 ${refreshing ? "animate-spin" : ""}`}
              />
              {refreshing ? "Refreshing..." : "Refresh"}
            </Button>
            {refreshing && (
              <span className="text-sm text-muted-foreground">
                Checking for new documents...
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Search and Filter Controls */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Search & Filter
            </div>
            <div className="text-sm font-normal text-muted-foreground">
              {filteredDocuments.length} of {documents.length} documents
              {refreshing && (
                <span className="ml-2 text-blue-600">
                  <RefreshCw className="h-3 w-3 animate-spin inline mr-1" />
                  Updating...
                </span>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Label htmlFor="search">Search Documents</Label>
              <Input
                id="search"
                placeholder="Search by title, message, or applicant name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="mt-1"
              />
            </div>
            <div className="w-full md:w-48">
              <Label htmlFor="status-filter">Status Filter</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {filteredDocuments.length > 0 && (
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Checkbox
                    id="select-all"
                    checked={
                      selectedDocuments.size === filteredDocuments.length &&
                      filteredDocuments.length > 0
                    }
                    onCheckedChange={handleSelectAll}
                  />
                  <Label htmlFor="select-all" className="text-sm font-medium">
                    Select All ({filteredDocuments.length})
                  </Label>
                </div>
                {selectedDocuments.size > 0 && (
                  <div className="text-sm text-muted-foreground">
                    {selectedDocuments.size} selected
                  </div>
                )}
              </div>
              {selectedDocuments.size > 0 && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleBulkDelete}
                  className="flex items-center gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  Delete Selected ({selectedDocuments.size})
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Documents Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredDocuments.map((document) => {
          const status = document.status || "pending";
          // Extract applicant name using dynamic field detection
          let applicantName = "Unknown";
          if (document.pdfData?.userData) {
            const userData = document.pdfData.userData;
            const userDataKeys = Object.keys(userData);

            // Find name fields dynamically
            const firstNameKey = userDataKeys.find((key) => {
              const lowerKey = key.toLowerCase();
              return lowerKey.includes("first") && lowerKey.includes("name");
            });

            const lastNameKey = userDataKeys.find((key) => {
              const lowerKey = key.toLowerCase();
              return (
                (lowerKey.includes("last") && lowerKey.includes("name")) ||
                lowerKey.includes("surname") ||
                lowerKey === "lastname"
              );
            });

            const middleNameKey = userDataKeys.find((key) => {
              const lowerKey = key.toLowerCase();
              return (
                (lowerKey.includes("middle") &&
                  (lowerKey.includes("name") ||
                    lowerKey.includes("initial"))) ||
                lowerKey === "middleinitial" ||
                lowerKey === "middle_initial"
              );
            });

            const firstName = firstNameKey ? userData[firstNameKey] : "";
            const lastName = lastNameKey ? userData[lastNameKey] : "";
            const middleInitial = middleNameKey ? userData[middleNameKey] : "";

            applicantName =
              [firstName, middleInitial, lastName]
                .filter((name) => name && name.trim())
                .join(" ")
                .trim() || "Unknown";
          }

          return (
            <Card
              key={document.id}
              className="hover:shadow-md transition-shadow"
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <Checkbox
                      checked={selectedDocuments.has(document.id)}
                      onCheckedChange={(checked) =>
                        handleDocumentSelect(document.id, checked as boolean)
                      }
                      onClick={(e) => e.stopPropagation()}
                    />
                    <div
                      className="flex items-center gap-2 cursor-pointer flex-1"
                      onClick={() => handleCardClick(document)}
                    >
                      <FileText className="h-5 w-5 text-blue-500" />
                      <CardTitle className="text-lg line-clamp-1">
                        {document.title}
                      </CardTitle>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(status)}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleArchiveDocument(document);
                      }}
                      className="h-8 w-8 p-0 text-blue-500 hover:text-blue-700 hover:bg-blue-50"
                      title="Archive Document"
                    >
                      <Archive className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteDocument(document);
                      }}
                      className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                      title="Delete Document"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2 text-sm text-muted-foreground mb-4">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span>{applicantName}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(document.createdAt)}</span>
                  </div>
                  {document.pdfData?.templateName && (
                    <div className="flex items-center gap-2">
                      <FileType className="h-4 w-4" />
                      <span>{document.pdfData.templateName}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredDocuments.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No documents found</h3>
            <p className="text-muted-foreground text-center mb-4">
              {searchTerm || statusFilter !== "all"
                ? "Try adjusting your search or filter criteria."
                : documents.length === 0
                ? "No documents have been uploaded yet. Documents will appear here automatically when clients upload files."
                : "All documents are filtered out by your current criteria."}
            </p>
            {documents.length === 0 && (
              <div className="flex flex-col items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={loading || refreshing}
                  className="flex items-center gap-2"
                >
                  <RefreshCw
                    className={`h-4 w-4 ${refreshing ? "animate-spin" : ""}`}
                  />
                  Check for new documents
                </Button>
                <p className="text-xs text-muted-foreground">
                  This page automatically refreshes every 30 seconds
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Single Delete Confirmation Dialog */}
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Document</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{documentToDelete?.title}"? This
              action cannot be undone and will permanently remove the document
              from the database.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteDocument}
              className="bg-red-500 hover:bg-red-600"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Delete Confirmation Dialog */}
      <AlertDialog
        open={isBulkDeleteDialogOpen}
        onOpenChange={setIsBulkDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Multiple Documents</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {selectedDocuments.size} selected
              document(s)? This action cannot be undone and will permanently
              remove all selected documents from the database.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmBulkDelete}
              className="bg-red-500 hover:bg-red-600"
            >
              Delete {selectedDocuments.size} Document(s)
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Archive Confirmation Dialog */}
      <AlertDialog
        open={isArchiveDialogOpen}
        onOpenChange={setIsArchiveDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Archive Document</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to archive "{documentToArchive?.title}"?
              This will move the document to the archives section where it can
              be restored later if needed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmArchiveDocument}
              className="bg-blue-500 hover:bg-blue-600"
            >
              Archive
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
